package com.mc.tool.caesar.vpm.util.vp;

class Vp6BgImgVideoData implements VpVideoData {

  private int width;
  private int height;
  private int alpha;

  Vp6BgImgVideoData(int width, int height, int alpha) {
    this.width = width;
    this.height = height;
    this.alpha = alpha;
  }

  @Override
  public String getName() {
    return "bg img";
  }

  @Override
  public int getTxId() {
    return Vp6Constants.BGIMG_TX_ID;
  }

  @Override
  public int getSourceIndex() {
    return 0;
  }

  @Override
  public int getCropIndex() {
    return 0;
  }

  @Override
  public int getLeft() {
    return 0;
  }

  @Override
  public int getTop() {
    return 0;
  }

  @Override
  public int getWidth() {
    return width;
  }

  @Override
  public int getHeight() {
    return height;
  }

  @Override
  public int getAlpha() {
    return alpha;
  }
}
