package com.mc.tool.caesar.vpm.util.vp;

class VpSimpleVideoData implements VpVideoData {

  private final int width;
  private final int height;
  private final int left;
  private final int top;
  private final int alpha;
  private final int txid;
  private final String name;
  private final int sourceIndex;
  private final int cropIndex;


  VpSimpleVideoData(int left, int top, int width, int height, int alpha, int txid) {
    this.width = width;
    this.height = height;
    this.left = left;
    this.top = top;
    this.alpha = alpha;
    this.txid = txid;
    this.name = "";
    this.sourceIndex = 0;
    this.cropIndex = 0;
  }

  VpSimpleVideoData(int left, int top, int width, int height, int alpha, int txid, String name, int sourceIndex, int cropIndex) {
    this.width = width;
    this.height = height;
    this.left = left;
    this.top = top;
    this.alpha = alpha;
    this.txid = txid;
    this.name = name;
    this.sourceIndex = sourceIndex;
    this.cropIndex = cropIndex;
  }

  @Override
  public String getName() {
    return name;
  }

  @Override
  public int getTxId() {
    return txid;
  }

  @Override
  public int getSourceIndex() {
    return sourceIndex;
  }

  @Override
  public int getCropIndex() {
    return cropIndex;
  }

  @Override
  public int getLeft() {
    return left;
  }

  @Override
  public int getTop() {
    return top;
  }

  @Override
  public int getWidth() {
    return width;
  }

  @Override
  public int getHeight() {
    return height;
  }

  @Override
  public int getAlpha() {
    return alpha;
  }
}
