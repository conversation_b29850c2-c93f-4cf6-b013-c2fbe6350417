CaesarOfficePage.title=Office
alert.can_not_save_scenario=Can not save scenario now!
alert.device_busy=<PERSON><PERSON> is busy now. Please try it later.
alert.reboot_alert.text=Please reconnect to the host device. The IP address has been changed to:
alert.reboot_alert.title=Reboot
alert.task.message=Reconnecting...
caesar_menu.configuration=Configuration
caesar_menu.download=Download Configuration
caesar_menu.refresh=Refresh
caesar_menu.save=Save Online Configuration
caesar_menu.upload=Upload Configuration
con_hotkey.apply=Apply
con_hotkey.cancel=Cancel
con_hotkey.con_hotkey_management=Con Hotkey Management
con_hotkey.con_list=Con List
con_hotkey.copy_btn=Copy the current hot key list for polling to other con
con_hotkey.table.id=ID
con_hotkey.table.name=Name
con_right.con_cpu_right_management=Con Rights Management
con_right.con_list=Con List
connect_device.connecting=Connecting {0}
connect_device.error.message=Fail to connect {0}!
connect_device.error.title=Fail to connect
connect_device.load_error=Fail to load!
connect_device.loading=Loading {0}...
connect_device.wrong_username_password=Wrong username password or no login permission!
copy_cpu_right_dialog.con_cpu_right.source_header.title=Available Con List
copy_cpu_right_dialog.con_cpu_right.target_header.title=Copy the list of configured Con
copy_cpu_right_dialog.con_cpu_right.title=Copy permissions are configured to other con
copy_cpu_right_dialog.user_cpu_right.source_header.title=Available User List
copy_cpu_right_dialog.user_cpu_right.target_header.title=Copy the list of configured User
copy_cpu_right_dialog.user_cpu_right.title=Replication permissions are configured for other users
copy_hotkey_dialog.con_hotkey.source_header.title=List of available Con
copy_hotkey_dialog.con_hotkey.target_header.title=Copy the configuration Con list
copy_hotkey_dialog.con_hotkey.title=Copy the current hot key list for polling to other controls
copy_macro_dialog.con_source_header=List of available cons
copy_macro_dialog.con_target_header=The list to copy the configuration
copy_macro_dialog.title=Copy Macro Configuration
copy_macro_dialog.user_source_header=List of available users
copy_macro_dialog.user_target_header=The list to copy the configuration
cpu_right.apply=Apply
cpu_right.cancel=Cancel
cpu_right.full_access=Full Access
cpu_right.menu.full=Assign Full Access rights
cpu_right.menu.no=Assign No Access rights
cpu_right.menu.video=Assign Video Access rights
cpu_right.name=Name
cpu_right.no_access=No Access
cpu_right.tip=Press key F for full access, V for video access and N for no access
cpu_right.user_cpu_right_management=User Rights Management
cpu_right.user_list=User List
cpu_right.video_access=Video Access
cross_screen.cancel=Cancel
cross_screen.column=Columns:
cross_screen.commit=Commit
cross_screen.commit.warn_content=The postion of RX and the control RX can not be changed after commit. Continue to commit?
cross_screen.commit.warn_title=Warning
cross_screen.control=Control
cross_screen.display=Red frame Hint(s)
cross_screen.mode=Mode
cross_screen.mode.auto=Auto
cross_screen.mode.manual=Manual
cross_screen.name=Name
cross_screen.row=Rows:
cross_screen.rx_list=Multi-screen RXs
cross_screen.rx_list.index=Index
cross_screen.rx_list.name=Name
cross_screen.setting=Setting
cross_screen.source_list_title=Video Source List
export.config=Device's configuration
export.graph=System Graph
export.report=Report
export.status=System Status
export.status_and_log=System Status And Log
export.status.fail=Fail to save!
export.status.ok=Save successfully!
export_report.empty_MessageNotify=Selection is empty!
export_report.empty_file_path=The file path is empty\uFF01
export_report.file_path=File Path\uFF1A
export_report.filechoose_title=Save the report
export_report.page1_title=Select the content
export_report.save_success=Save the report successfully!
export_report.wizard_title=Config report
hostconfiguration.activate_tab=Activate Configuration
hostconfiguration.general_apply_btn=Apply
hostconfiguration.general_cancel_btn=Cancel
hostconfiguration.general_tab=General Configuration
hostconfiguration.grid_tab=Matrix Grid Configuration
hostconfiguration.network_apply_btn=Apply
hostconfiguration.network_cancel_btn=Cancel
hostconfiguration.network_tab=Network Configuration
hostconfiguration.redundancy_apply_btn=Apply
hostconfiguration.redundancy_cancel_btn=Cancel
hostconfiguration.redundancy_tab=Dual-host Redundant
hostconfiguration.snmp_apply_btn=Apply
hostconfiguration.snmp_cancel_btn=Cancel
hostconfiguration.snmp_tab=SNMP Configuration
hostconfiguration.syslog_tab=SystemLog
hotkey_selection_view.source_header=Access side polling list is available
hotkey_selection_view.target_header=Polling hot key list
macro_key.apply=Apply
macro_key.cancel=Cancel
macro_key.con_access_block_title=RX's Macro List
macro_key.con_source_list_title=RX List
macro_key.copy_macro_list=Copy Macro List
macro_key.copy_to_other_rx=Copy Macro Configuration To other RXs
macro_key.copy_to_other_user=Copy Macro Configuration To other Users
macro_key.current_con_device=Current RX Device
macro_key.current_user=Current User
macro_key.delete_macro_list=Delete Macro List
macro_key.delete_macro_list.delete_all=Delete All
macro_key.delete_macro_list.delete_confirm_content=Delete selected key's macro or delete all keys'?
macro_key.delete_macro_list.delete_confirm_title=Delete macro
macro_key.delete_macro_list.delete_current=Delete Selected
macro_key.disconnected_cpu=Disconnect TX
macro_key.function=Function
macro_key.index=Index
macro_key.logout=Logout
macro_key.macro=Macro
macro_key.name=Name
macro_key.paste_macro_list=Paste Macro List
macro_key.user_access_block_title=User's Macro List
macro_key.user_source_list_title=User List
matrixgrid.bind.alert.content.text=5656 port has been used
matrixgrid.bind.alert.title.text=Warning
matrixgrid.connect=Connect
matrixgrid.matrix=Matrix
matrixgrid.wizard.hostconfig.delete=Delete
matrixgrid.wizard.hostconfig.duplicate=Duplicate ip address
matrixgrid.wizard.hostconfig.matrix=Matrix
matrixgrid.wizard.hostconfig.name_dulplicate=Name dulplicate
matrixgrid.wizard.hostconfig.name_empty=Name is empty
matrixgrid.wizard.hostconfig.name_format_error=Name error
matrixgrid.wizard.hostconfig.name_too_long=Name is too long
matrixgrid.wizard.hostconfig.port_error=Port is error
matrixgrid.wizard.hostconfig.unaccessable=Unaccessable
matrixgrid.wizard.hostconfig.uncomplete=Data uncomplete
matrixgrid.wizard.hostconfig.valid=Valid
matrixgrid.wizard.hostconfig.validate=Validate
matrixgrid.wizard.indicator.activate=Activate
matrixgrid.wizard.indicator.gridname=Grid Name
matrixgrid.wizard.indicator.gridsystem=Grid System
matrixgrid.wizard.indicator.host=Host Configuration
matrixgrid.wizard.indicator.idprocess=ID Process
matrixgrid.wizard.indicator.prepare=Prepare
offline_manager.delete_btn=Delete
offline_manager.device=Device
offline_manager.empty_alert.text=Please select the item to delete!
offline_manager.empty_alert.title=Prompt
offline_manager.ext_name=Extender
offline_manager.name=Device
offline_manager.title=Offline Device Management
page.con_cpu_right=Con Rights
page.con_hotkey_configuration=ConHotkeyConfiguration
page.con_macro_view=RX's Macro
page.refreshing=Refreshing...
page.switch=Switch
page.switch_to_operation=Switch to System operation page?
page.user_cpu_right=User Rights
page.user_group_cpu_right=UserGroup Rights
page.user_macro_view=User's Macro
report.assignment=Assignment
report.con_acl=Access Control
report.con_devices=Con Devices
report.con_favorites=Favorites
report.con_macros=Macros
report.cpu_devices=Cpu Devices
report.ext_units=Extender Units
report.matrix_view=Matrix View
report.select_all=Select All
report.system=System
report.user=User
report.user_acl=Access Control
report.user_favorites=Favorites
report.user_macros=Macros
save_config.alert.download_complete=Download complete
save_config.alert.download_failed=Download failed
save_config.alert.title=Information
save_config.alert.upload_complete=Upload complete
save_config.alert.upload_failed=Upload failed
save_config.download_title=Download
save_config.page1.tips1=Procedure
save_config.page1.tips2=1.Connection
save_config.page1.tips3=2.Select configuration file
save_config.page1.tips4=2.Select the configuration slot
save_config.page2.address=Host name /IP address
save_config.page2.errorMsg.text1=The user has no admin rights. Upload not allowed.
save_config.page2.errorMsg.text2=user not exist!
save_config.page2.errorMsg.text3=connect failed
save_config.page2.errorMsg.text4=Matrix busy
save_config.page2.errorMsg.text5=User name or password error!
save_config.page2.password=Password
save_config.page2.user=User
save_config.page2.verify=Verify
save_config.page3.activate_alert.text=Activate configuration file,Host must restart,Are you sure you want to activate configuration?
save_config.page3.activate_alert.title=Active configuration
save_config.page3.activate_checkbox=Activate the configuration after uploading (host will restart!)
save_config.page3.addressCol=IP Address
save_config.page3.download=Download
save_config.page3.downloadBtn=Download
save_config.page3.fileCol=File
save_config.page3.infoCol=Info
save_config.page3.nameCol=Name
save_config.page3.title=Select configuration file
save_config.page3.uploadBtn=Upload
save_config.page3.versionCol=Version
save_config.upload_title=Upload
searchForDevices.title=Search For Devices
toolbar.meeting_room=Meeting Room Layout
toolbar.save_scenario=Save Scenario
user_group.name=Name
user_group_right.delete_button=Delete User Group
user_group_right.edit_button=Edit User Group
user_group_right.new_button=New user group
user_group_right.user_group_cpu_right_management=UserGroup Right Management
user_group_right.user_group_list=UserGroup List
videowall.check.vp6_screen_layer_limit=The count of layers in a screen must not exceed {0}!
videowall.check.vp6_layer_limit=The count of layers in a VP6 must not exceed {0}!
videowall.check.vp6_window_limit=The count of 2K windows in a VP6 must not exceed {0}! The count of 4K/DHDMI windows in a VP6 must not exceed {1}! 
videowall.check.vp7_screen_layer_limit=The count of layers in a screen must not exceed {0} at output mode {1}!
videowall.check.vp7_window_limit=The count of 2K/4K30/DHDMI signals in a VP7 must not exceed {0}! The count of 4K60 signals in a VP7 must not exceed {1}!
videowall.check.vp7_invalid_screen=Current output mode is {0}. The screen is invalid!
videowall.mode=Mode
videowall.mode.auto=Auto
videowall.mode.manual=Manual
videowall.resync=Resync Video
videowall.output=Output
videowall.output.clock=Clock
videowall.output.enable=Enable
videowall.output.horz_active_video_time=Horizontal Resolution
videowall.output.horz_back_porch=Horizontal Back porch
videowall.output.horz_front_porch=Horizontal Front porch
videowall.output.horz_polarity=Horizontal Polarity
videowall.output.horz_sync=Horizontal SYNC
videowall.output.vert_active_video_time=Vertical Resolution
videowall.output.vert_back_porch=Vertical Back porch
videowall.output.vert_front_porch=Vertical Front porch
videowall.output.vert_polarity=Vertical Polarity
videowall.output.vert_sync=Vertical SYNC
videowall.scenario.limit_error=Only can save {0} scenarios at most!
wizard_pane.next_btn=Next
wizard_pane.previous_btn=Back
export.status.saving=Saving Status
systemedit=
systemedit.master=Master
systemedit.slave=Slave
systemedit.gridline=Grid Line
matrixgrid.wizard.hostconfig.port_too_much=Too much ports
multiscreen=
multiscreen.row=Row
multiscreen.column=Column
multiscreen.layout=RX Layout
toolbar.save_as_scenario=Save As Scenario
multiscreen.connection=
multiscreen.connection.full=Full Connect
multiscreen.connection.video=Video Connect
multiscreen.rx_list=RX List
multiscreen.video_conn=Video Connect
multiscreen.full_conn=Full Connect
systemedit.list=
systemedit.list.index=Index
systemedit.list.id=ID
systemedit.list.device=Device
systemedit.list.port=Port
systemedit.list.type=Type
systemedit.list.serial=Serial
systemedit.exporting=Exporting extender info
systemedit.exportsuccess=Export successfully
systemedit.exporterror=Fail to export
systemedit.importing=Importing extender info
systemedit.importsuccess=Import Successfully
systemedit.importerror=Fail to import
systemedit.importerror.formaterror=Some items' format are error.
systemedit.importerror.typeerror=Some items are not exist or types are mismatch.
systemedit.importerror.nameerror=Some items' names are error.
systemedit.importerror.iderror=Some items' ids are error.
systemedit.importerror.unknownerror=Unknown error.
systemedit.title=
systemedit.title.success=Success
systemedit.title.fail=Fail
systemedit.switch=Switch To Matrix
systemedit.topological=Grid Topological Graph
videowall.check.vpcon_4k_window_limit=The count of 4k windows in a VP6 must not exceed {0}!
videowall.check.vpcon_2k_window_limit=The count of 2k windows in a VP6 must not exceed {0}!
videowall.not_created=Not created
videowall.bgimg.uploadpath=Background Image Path:
videowall.bgimg.selectbtn=Select
videowall.bgimg.upload=Upload
videowall.bgimg.cancel=Cancel
videowall.bgimg.title=Upload Background Image
cross_screen.fail_to_create=Fail to create Cross Screen Group for no enough space.
connect_device.error_version=Version {0} is not supported. Please update to new version VPM software.
check.try=Initializing data. Please try to refesh.
user.videowallright.selected=Selected Video Walls
user.videowallright.unselected=Available Video Walls
cross_screen.mode.irregular=irregular
systemedit.switching.index=Index
systemedit.switching.id=ID
systemedit.switching.device=Device
systemedit.switching.port=Port
systemedit.switching.mode=SwitchingMode
systemedit.switching.ip=PDU IP
systemedit.switching.outlet=Outlet
systemedit.switching.operation=Operation
systemedit.switching.poweron=PowerOn
systemedit.switching.poweroff=PowerOff
systemedit.switching.reboot=Reboot
systemedit.switching.poweron.alert=Do you want to start the PC remotely?
systemedit.switching.poweroff.alert=Do you force a shutdown?
systemedit.switching.reboot.alert=Do you force a restart?
systemedit.switching.networkwake=Network Wake
systemedit.switching.pcpowercontrol=PC Power Control
systemedit.switching.disable=Disable
systemedit.switching.editPduIpDialog.title=SET PDU IP
systemedit.speed.index=Index
systemedit.speed.name=Name
systemedit.speed.speed=Speed
videowall.logiclayout.enable=Enable
videowall.logiclayout.row=Logic Row
videowall.logiclayout.column=Logic Colum
systemedit.page.selector.graph=graph
systemedit.page.selector.list=list
systemedit.page.selector.switch=power on&off
systemedit.page.selector.speed=Speed Setting
systemedit.exporting.optModule=Exporting Optical Module info
txGroupsManager.title=Invalid TXRXGroups Management
txGroupsManager.selectAll=SelectAll
page.saving=Saving...
videowall.check.vpcon_offline=Communication Error!
hostconfiguration.event_tab=EventConfiguration
hostconfiguration.event_apply_btn=Apply
hostconfiguration.event_cancel_btn=Cancel
videowall.check.screen_duplicate=VideoWall have duplicate screens
multiscreen.connection.disconnect=Disconnect
cross_screen.mode.linkage=MultiView linkage mode
enable=enable
systemedit.page.selector.offline=OfflineDevice
name=Name
offline_manager.delete_selected=Delete selected
offline_manager.delete_checked=Delete checked
offline_manager.delete_checked.alert=Are you sure to delete the checked item?
check.matrix_version=The version of the master control FPGA does not match; please upgrade.
videowall.audio_group.dialog.title=Please select the audio group
menu_disconnect=Disconnect
resWidth-label=Width
resHeight-label=Height
videowall.resync.hint=Click button on right
videowall.resync.confirm=Confirm to resync?

