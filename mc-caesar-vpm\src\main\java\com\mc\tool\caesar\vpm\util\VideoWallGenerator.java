package com.mc.tool.caesar.vpm.util;

import com.google.gson.Gson;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ResolutionData;
import com.mc.tool.caesar.api.datamodel.SourceCropData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData.VpResolution;
import com.mc.tool.caesar.api.utils.SampleModelCreater.SampleModelInfo;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarDataUtility;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarVideoData;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarVideoWallData;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarData2VisualDataModel;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMatrix;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.VpGroup;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import javafx.util.Pair;
import lombok.Getter;

/**
 * .
 */
public class VideoWallGenerator {

  @Getter private VisualEditModel model;
  @Getter private CaesarDeviceController controller;
  @Getter private CaesarVideoWallData videoWallData;

  /**
   * 视频墙类型.
   */
  public enum VideoWallType {
    VP6,
    VP7
  }

  /**
   * 从资源文件创建video wall generator.
   *
   * @param classLoader classloader
   * @param path 资源文件路径.
   * @return videowall generator
   */
  public static VideoWallGenerator createVideoWallGeneratorFromResource(
      ClassLoader classLoader, String path, VideoWallType type) {
    InputStream stream = classLoader.getResourceAsStream(path);
    InputStreamReader reader = new InputStreamReader(stream, StandardCharsets.UTF_8);
    Gson gson = new Gson();
    VideoWallConfig videoWallConfig = gson.fromJson(reader, VideoWallConfig.class);
    videoWallConfig.type = type;
    return new VideoWallGenerator(new StatusConfig(), videoWallConfig);
  }

  /**
   * 从资源文件创建video wall generator.
   *
   * @param classLoader classloader
   * @param path 资源文件路径.
   * @param statusConfig 状态包配置
   * @return videowall generator
   */
  public static VideoWallGenerator createVideoWallGeneratorFromResource(
      ClassLoader classLoader, String path, StatusConfig statusConfig) {
    InputStream stream = classLoader.getResourceAsStream(path);
    InputStreamReader reader = new InputStreamReader(stream, StandardCharsets.UTF_8);
    Gson gson = new Gson();
    VideoWallConfig videoWallConfig = gson.fromJson(reader, VideoWallConfig.class);

    return new VideoWallGenerator(statusConfig, videoWallConfig);
  }

  /**
   * 创建一个没有窗口的videowall generator.
   */
  public static VideoWallGenerator createNoWindowVideoWallGenerator(VideoWallType type) {
    VideoWallConfig videoWallConfig = new VideoWallConfig();
    videoWallConfig.type = type;
    videoWallConfig.width = 1920;
    videoWallConfig.height = 1080;
    videoWallConfig.vert = 2;
    videoWallConfig.horz = 2;
    videoWallConfig.screens = new VideoWallGenerator.ScreenConfig[1];
    videoWallConfig.screens[0] = new VideoWallGenerator.ScreenConfig();
    videoWallConfig.screens[0].binding = new VideoWallGenerator.ScreenBinding();
    videoWallConfig.screens[0].binding.locations = new int[] {0, 1, 2, 3};
    return new VideoWallGenerator(new StatusConfig(), videoWallConfig);
  }

  /**
   * 创建videowall generator.
   *
   * @param statusConfig 状态包配置.
   * @param config 视频墙配置
   */
  public VideoWallGenerator(StatusConfig statusConfig, VideoWallConfig config) {
    SampleModelInfo info = CaesarSampleStatusCreater.createSimpleSingleStatusInfo();
    info.setTxCount(config.getVideoSourceCount() * 2); // 预留足够的tx
    info.setVp6Count(config.getVp6Count());
    info.setVp6PortSize(statusConfig.vp6PortSize);
    info.setVp7Count(config.getVp7Count());
    CaesarDeviceController deviceController =
        CaesarSampleStatusCreater.createDeviceController(info);
    VisualEditModel model = new VisualEditModel();
    model.init();

    CaesarData2VisualDataModel converter = new CaesarData2VisualDataModel();
    converter.setModel(model);
    converter.setDeviceController(deviceController);
    converter.loadAll();

    List<CpuData> txList =
        new ArrayList<>(deviceController.getDataModel().getConfigDataManager().getActiveCpus());
    for (int i = 0; i < config.sources.length; i++) {
      if (config.sources[i].isDhdmi) {
        txList.get(i).getExtenderData(0).getExtenderStatusInfo().setInterfaceCount(2);
        ResolutionData resolutionData = new ResolutionData();
        resolutionData.setWidth(1920);
        resolutionData.setHeight(1080);
        txList.get(i).getExtenderData(0).setResolution(1, resolutionData);
      }
    }
    List<VpConsoleData> vps =
        new ArrayList<>(
            deviceController.getDataModel().getConfigDataManager().getActiveVpconsolses());
    CaesarVideoWallData data = createVideoWall(deviceController.getDataModel(), model, config, vps);
    this.model = model;
    this.controller = deviceController;
    this.videoWallData = data;
  }

  /**
   * 创建视频墙.
   *
   * @param model model
   * @param config 视频墙配置
   * @param vps 视频墙使用的vpcon
   * @return 视频墙数据.
   */
  public static CaesarVideoWallData createVideoWall(
      CaesarSwitchDataModel switchDataModel, VisualEditModel model, VideoWallConfig config, List<VpConsoleData> vps) {
    CaesarMatrix matrix = CaesarDataUtility.getFirstMatrix(model);
    // 配置视频墙
    CaesarVideoWallData data = new CaesarVideoWallData();
    data.init();
    // 先写1触发screens的生成.
    data.setHorzCount(1);
    data.setHorzCount(config.horz);
    data.setVertCount(1);
    data.setVertCount(config.vert);
    data.setResolutionWidth(config.width);
    data.setResolutionHeight(config.height);
    if (config.bgImgWidth > 0 && config.bgImgHeight > 0) {
      data.getOsdData().setEnableBgImg(true);
      data.getOsdData().setBgImgWidth(config.bgImgWidth);
      data.getOsdData().setBgImgHeight(config.bgImgHeight);
    }
    data.getOsdData().setAnalogAudioVolume(config.analogAudioVolume);
    data.setCompensationScaleThreshold(config.compensationScaleThreshold);
    data.setLeftCompensation(config.leftCompensation);
    data.setRightCompensation(config.rightCompensation);
    data.setTopCompensation(config.topCompensation);
    data.setBottomCompensation(config.bottomCompensation);
    if (config.widths.length > 0 && config.heights.length > 0) {
      data.setEnableMultipleResolution(true);
      data.setResolutionWidths(Arrays.stream(config.widths).boxed().collect(Collectors.toList()));
      data.setResolutionHeights(Arrays.stream(config.heights).boxed().collect(Collectors.toList()));
    }
    if (config.horzMargins.length > 0 && config.vertMargins.length > 0) {
      data.setHorzMargins(
          Arrays.stream(config.horzMargins).boxed().collect(Collectors.toList()));
      data.setVertMargins(
          Arrays.stream(config.vertMargins).boxed().collect(Collectors.toList()));
    }

    VpResolution vpResolution = new VpResolution();
    vpResolution.setWidth(config.width);
    vpResolution.setHeight(config.height);
    int vpconCount = config.type == VideoWallType.VP6 ? config.getVp6Count() : config.getVp7Count();
    for (int i = 0; i < vpconCount; i++) {
      VpConsoleData vpConsoleData = vps.get(i);
      ScreenConfig screenConfig = config.screens[i];
      // 配置分辨率
      for (int j = 0; j < vpConsoleData.getOutPortCount(); j++) {
        vpConsoleData.setOutResolution(j, vpResolution);
      }

      // 绑定屏幕
      VpGroup vpGroup = matrix.findVpGroup(vpConsoleData.getId());
      for (int j = 0; j < vpConsoleData.getOutPortCount(); j++) {
        int index = screenConfig.binding.locations[j];
        if (index >= 0) {
          data.getScreens().get(index).setBingdingScreen(new Pair<>(vpGroup, j));
        }
      }
    }

    // 添加窗口
    List<VisualEditTerminal> terminals = matrix.getAllTxChildTerminal();
    for (VideoConfig videoConfig : config.videos) {
      CaesarVideoData videoData = new CaesarVideoData();
      videoData.getWidth().set(videoConfig.ow);
      videoData.getHeight().set(videoConfig.oh);
      videoData.getXpos().set(videoConfig.xpos);
      videoData.getYpos().set(videoConfig.ypos);
      videoData.getAlpha().set(videoConfig.alpha);
      videoData.getSourceIndex().set(videoConfig.sourceIndex);
      data.addVideo(videoData);

      CaesarCpuTerminal terminal = (CaesarCpuTerminal) terminals.get(videoConfig.sourceid);
      videoData.getSource().set(terminal);
      ResolutionData resolutionData = new ResolutionData();
      resolutionData.setWidth(videoConfig.iw);
      resolutionData.setHeight(videoConfig.ih);
      if (terminal.getExtenderData() != null) {
        terminal.getExtenderData().setResolution(0, resolutionData);
        if (config.sources.length > videoConfig.sourceid && config.sources[videoConfig.sourceid].is4k60) {
          terminal.getExtenderData().getExtenderStatusInfo().setVideoResolutionType(
              CaesarConstants.Extender.ExtenderVideoResolutionType.VIDEO_4K_60HZ);
        } else if (videoConfig.iw > 1920) {
          terminal.getExtenderData().getExtenderStatusInfo().setVideoResolutionType(
              CaesarConstants.Extender.ExtenderVideoResolutionType.VIDEO_4K_30HZ);
        } else {
          terminal.getExtenderData().getExtenderStatusInfo().setVideoResolutionType(
              CaesarConstants.Extender.ExtenderVideoResolutionType.VIDEO_2K);
        }
      }

      if (videoConfig.clipLeft > 0
          || videoConfig.clipTop > 0
          || videoConfig.clipRight > 0
          || videoConfig.clipBottom > 0) {
        SourceCropData cropData = switchDataModel.getConfigDataManager().getFreeSourceCropData();
        if (cropData != null) {
          cropData.setCpuIndex(terminal.getCpuData().getOid() + 1);
          cropData.setSourceIndex(videoConfig.sourceIndex);
          cropData.setLeft(videoConfig.clipLeft);
          cropData.setTop(videoConfig.clipTop);
          cropData.setRight(videoConfig.clipRight);
          cropData.setBottom(videoConfig.clipBottom);
          videoData.getCropIndex().set(cropData.getOid() + 1);
        }
      }
    }
    return data;
  }

  /**
   * .
   */
  public static class StatusConfig {

    public int vp6PortSize = 8;
  }

  static class VideoWallConfig {
    public VideoWallType type = VideoWallType.VP6;
    public int horz;
    public int vert;
    public int width;
    public int height;
    public int bgImgWidth = 0;
    public int bgImgHeight = 0;
    public int[] widths = new int[0];
    public int[] heights = new int[0];
    public int[] horzMargins = new int[0];
    public int[] vertMargins = new int[0];
    public VideoConfig[] videos = new VideoConfig[0];
    public ScreenConfig[] screens = new ScreenConfig[0];
    public SourceConfig[] sources = new SourceConfig[0];
    public int compensationScaleThreshold = 0;
    public int leftCompensation = 0;
    public int rightCompensation = 0;
    public int topCompensation = 0;
    public int bottomCompensation = 0;
    public int analogAudioVolume = 0;

    public int getVideoSourceCount() {
      if (videos == null) {
        return 0;
      }
      int maxid = 0;
      for (VideoConfig config : videos) {
        maxid = Math.max(maxid, config.sourceid);
      }
      return maxid + 1;
    }

    public int getVp6Count() {
      if (screens == null || type != VideoWallType.VP6) {
        return 0;
      }
      return screens.length;
    }

    public int getVp7Count() {
      if (screens == null || type != VideoWallType.VP7) {
        return 0;
      }
      return screens.length;
    }
  }

  static class ScreenBinding {

    public int[] locations = new int[0]; // 各个输出端口在屏幕的位置
  }

  static class ScreenConfig {

    public ScreenBinding binding = null;
  }

  static class VideoConfig {

    public int sourceid; // 从0开始
    public int sourceIndex = 0;
    public int iw;
    public int ih;
    public int ow;
    public int oh;
    public int xpos;
    public int ypos;
    public double alpha;
    public int clipLeft = 0;
    public int clipTop = 0;
    public int clipRight = 0;
    public int clipBottom = 0;
  }

  static class SourceConfig {

    public boolean isDhdmi = false;
    public boolean is4k60 = false;
  }
}
