package com.mc.tool.caesar.vpm.pages.operation.videowall.view;

import com.google.common.base.Strings;
import com.mc.common.util.PlatformUtility;
import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.TxRxGroupData;
import com.mc.tool.caesar.api.datamodel.TxRxGroupDataType;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.api.utils.ExtendedSwitchUtility;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.operation.rearrange.RearrangeDialog;
import com.mc.tool.caesar.vpm.pages.operation.videowall.controller.Bundle;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarOutputData;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarTestFrameData;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarTestFrameMode;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarVideoData;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarVideoWallData;
import com.mc.tool.caesar.vpm.pages.operation.view.CaesarOperationFuncPane;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMatrix;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbTxTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarVideoWallFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.VpGroup;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.interfaces.ViewControllable;
import com.mc.tool.framework.operation.videowall.controller.VideoWallControllable;
import com.mc.tool.framework.operation.videowall.datamodel.LogicLayoutData;
import com.mc.tool.framework.operation.videowall.view.VideoWallOperationView;
import com.mc.tool.framework.operation.view.VideoSourceTree.SourceMode;
import com.mc.tool.framework.systemedit.datamodel.VideoWallFunc;
import com.mc.tool.framework.systemedit.datamodel.VideoWallFunc.PublishMode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.datamodel.preview.VideoPreviewFunc;
import com.mc.tool.framework.utility.AggregatedObservableArrayList;
import com.mc.tool.framework.utility.I18nUtility;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.utility.UndecoratedAlert;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.ResourceBundle;
import java.util.stream.Collectors;
import javafx.beans.binding.Bindings;
import javafx.beans.binding.BooleanBinding;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.ReadOnlyBooleanWrapper;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Node;
import javafx.scene.control.AlertEx;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonType;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableRow;
import javafx.scene.control.TableView;
import javafx.scene.control.ToggleButton;
import javafx.scene.input.ClipboardContent;
import javafx.scene.input.Dragboard;
import javafx.scene.input.MouseEvent;
import javafx.scene.input.TransferMode;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Pane;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;
import javafx.util.StringConverter;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.BooleanPropertyItem2;
import org.controlsfx.control.EnumPropertyItem;
import org.controlsfx.control.ExPropertyEditorFactory;
import org.controlsfx.control.NumberPropertyItem;
import org.controlsfx.control.NumberPropertyItem.NumberConverter;
import org.controlsfx.control.ObjectPropertyItem;
import org.controlsfx.control.PropertySheet;
import org.controlsfx.control.TextBlockPropertyItem;

/**
 * .
 */
@Slf4j
public class CaesarVideoWallFuncPane extends CaesarOperationFuncPane
    implements Initializable, ViewControllable {

  private static class AlphaNumberConverter implements NumberConverter {
    @Override
    public Number toValue(Number value) {
      return (int) (value.doubleValue() * 255);
    }

    @Override
    public Number toDisplay(Number value) {
      return (int) (value.intValue() * 100 / 255.0 + 0.5) / 100.0;
    }
  }
  
  private final CaesarVideoWallFunc func;

  @FXML
  private CaeasrVideoWallVideoSourceTree sourceTree;
  @FXML
  private HBox autoArrangeBox;
  @FXML
  private Button saveScenarioBtn;
  @FXML
  private Button saveAsScenarioBtn;
  @FXML
  private Button configBtn;
  @FXML
  private Button publishBtn;
  @FXML
  private ToggleButton preWindowBtn;
  @FXML
  private VBox videoPreviewContainer;
  @FXML
  private PropertySheet logicLayoutPropertySheet;
  @FXML
  private PropertySheet videoLayoutPropertySheet;
  @FXML
  private PropertySheet audioGroupPropertySheet;
  @FXML
  private PropertySheet testScreenPropertySheet;
  @FXML
  private PropertySheet configPropertySheet;
  @FXML
  private PropertySheet layoutPropertySheet;
  @FXML
  private PropertySheet outputPropertySheet;
  @FXML
  private VBox txVbox;
  @FXML
  private VBox rxVbox;
  @FXML
  private VBox txPropertyBox;
  @FXML
  private VBox rxPropertyBox;
  @FXML
  private TableView<VpConsoleData> tableView;
  @FXML
  private TableColumn<VpConsoleData, String> cellIndex;
  @FXML
  private TableColumn<VpConsoleData, String> cellName;
  @FXML
  private TableColumn<VpConsoleData, String> cellDpi;

  @FXML
  private TableView<CaesarVideoData> windowsTable;
  @FXML
  private TableColumn<CaesarVideoData, String> windowNameColumn;
  @FXML
  private TableColumn<CaesarVideoData, VisualEditTerminal> windowSourceColumn;

  private ObservableList<VisualEditNode> observableChildrenList =
      FXCollections.observableArrayList();

  private AggregatedObservableArrayList<VpConsoleData> vpGroupOutPortsList =
      new AggregatedObservableArrayList<>("vpGroupOutPortsList");

  private ObservableList<VpConsoleData> vpConDataList = vpGroupOutPortsList.getAggregatedList();

  private BooleanProperty isSceenShowMode = new SimpleBooleanProperty(false);

  private CaesarDeviceController deviceController;

  private VideoWallOperationView videoWallView = null;

  private BooleanProperty editableProperty = new SimpleBooleanProperty(true);
  private WeakAdapter weakAdapter = new WeakAdapter();

  /**
   * Constructor.
   *
   * @param func             func
   * @param model            model
   * @param deviceController device controller
   */
  public CaesarVideoWallFuncPane(
      CaesarVideoWallFunc func, VisualEditModel model, CaesarDeviceController deviceController) {
    super(model);
    this.func = func;
    this.deviceController = deviceController;
    editableProperty.set(deviceController.getCaesarUserRight().isVideoWallEditable(func));
    func.getVideoWallIndexProperty()
        .addListener(
            weakAdapter.wrap(
                (obs, oldVal, newVal) ->
                    editableProperty.set(
                        deviceController.getCaesarUserRight().isVideoWallEditable(func))));
    URL location =
        Thread.currentThread()
            .getContextClassLoader()
            .getResource(
                "com/mc/tool/caesar/vpm/pages/operation/videowall/videowall_func_panel.fxml");
    FXMLLoader loader = new FXMLLoader(location);
    loader.setRoot(this);
    loader.setController(this);
    loader.setResources(I18nUtility.getI18nBundle("operation"));
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load videowall_func_panel.xml", exception);
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    videoWallView = new VideoWallOperationView(systemEditModel, func);
    videoWallView.getControllable().setDeviceController(deviceController);
    getFunctionViewContent().set(videoWallView.getView());

    observableChildrenList = func.getObservableChildren();
    vpGroupOutPortsList.clearList();
    for (VisualEditNode node : observableChildrenList) {
      if (node instanceof VpGroup) {
        VpGroup vpGroup = (VpGroup) node;
        vpGroupOutPortsList.appendList(vpGroup.getOutPortsList());
      }
    }
    observableChildrenList.addListener(
        weakAdapter.wrap(
            (ListChangeListener<VisualEditNode>)
                change -> {
                  while (change.next()) {
                    vpGroupOutPortsList.clearList();
                    for (VisualEditNode node : observableChildrenList) {
                      if (node instanceof VpGroup) {
                        VpGroup vpGroup = (VpGroup) node;
                        vpGroupOutPortsList.appendList(vpGroup.getOutPortsList());
                      }
                    }
                  }
                }));

    sourceTree.init(
        deviceController,
        systemEditModel,
        new SimpleObjectProperty<>(func),
        SourceMode.ALL,
        (terminal) ->
            terminal.isOnline()
                && terminal.isTx()
                && !(terminal instanceof CaesarUsbTxTerminal)
                && !(!func.isVp7() && terminal instanceof CaesarCpuTerminal && ((CaesarCpuTerminal) terminal).getResolutionType()
                == CaesarConstants.Extender.ExtenderVideoResolutionType.VIDEO_4K_60HZ));

    cellIndex.setCellFactory(col -> new IndexCell());
    cellName.setCellValueFactory(feature -> feature.getValue().nameProperty());
    cellDpi.setCellValueFactory(feature -> feature.getValue().outResolutionProperty());

    autoArrangeBox.managedProperty().bind(autoArrangeBox.visibleProperty());
    autoArrangeBox.visibleProperty().bind(editableProperty);

    publishBtn.managedProperty().bind(publishBtn.visibleProperty());
    publishBtn
        .visibleProperty()
        .bind(
            func.getPublishModeProperty().isEqualTo(VideoWallFunc.PublishMode.MANUAL).and(editableProperty));
    saveScenarioBtn.managedProperty().bind(saveScenarioBtn.visibleProperty());
    saveScenarioBtn
        .visibleProperty()
        .bind(
            editableProperty.and(
                new ReadOnlyBooleanWrapper(videoWallView.getControllable().canSaveScenario())));
    saveAsScenarioBtn.managedProperty().bind(saveAsScenarioBtn.visibleProperty());
    saveAsScenarioBtn
        .visibleProperty()
        .bind(
            editableProperty.and(
                new ReadOnlyBooleanWrapper(videoWallView.getControllable().canSaveScenario())));
    configBtn.managedProperty().bind(configBtn.visibleProperty());
    configBtn.setVisible(false);

    preWindowBtn.managedProperty().bind(preWindowBtn.visibleProperty());
    preWindowBtn.visibleProperty().bind(saveScenarioBtn.visibleProperty());
    preWindowBtn
        .selectedProperty()
        .addListener(
            weakAdapter.wrap(
                (obs, oldVal, newVal) -> {
                  if (newVal) {
                    videoWallView.getControllable().setAreaSelectionMode();
                  } else {
                    videoWallView.getControllable().setNodeSelectionMode();
                  }
                }));

    // video preview
    videoPreviewContainer.managedProperty().bind(videoPreviewContainer.visibleProperty());
    videoPreviewContainer
        .visibleProperty()
        .bind(
            Bindings.size(
                    systemEditModel
                        .getAllOnlineFuncs()
                        .filtered((func) -> func instanceof VideoPreviewFunc))
                .greaterThan(0));
    // 选中时预览

    logicLayoutPropertySheet.setPropertyEditorFactory(new ExPropertyEditorFactory());
    videoLayoutPropertySheet.setPropertyEditorFactory(new ExPropertyEditorFactory());
    layoutPropertySheet.setPropertyEditorFactory(new ExPropertyEditorFactory());
    outputPropertySheet.setPropertyEditorFactory(new ExPropertyEditorFactory());
    configPropertySheet.setPropertyEditorFactory(new ExPropertyEditorFactory());
    testScreenPropertySheet.setPropertyEditorFactory(new ExPropertyEditorFactory());
    audioGroupPropertySheet.setPropertyEditorFactory(new ExPropertyEditorFactory());

    logicLayoutPropertySheet.setModeSwitcherVisible(false);
    logicLayoutPropertySheet.setSearchBoxVisible(false);
    logicLayoutPropertySheet.disableProperty().bind(editableProperty.not());
    videoLayoutPropertySheet.setModeSwitcherVisible(false);
    videoLayoutPropertySheet.setSearchBoxVisible(false);
    videoLayoutPropertySheet.disableProperty().bind(editableProperty.not());
    audioGroupPropertySheet.setModeSwitcherVisible(false);
    audioGroupPropertySheet.setSearchBoxVisible(false);
    audioGroupPropertySheet.disableProperty().bind(editableProperty.not());
    testScreenPropertySheet.setModeSwitcherVisible(false);
    testScreenPropertySheet.setSearchBoxVisible(false);
    testScreenPropertySheet.disableProperty().bind(editableProperty.not());
    configPropertySheet.setModeSwitcherVisible(false);
    configPropertySheet.setSearchBoxVisible(false);
    configPropertySheet.disableProperty().bind(editableProperty.not());
    layoutPropertySheet.setModeSwitcherVisible(false);
    layoutPropertySheet.setSearchBoxVisible(false);
    layoutPropertySheet.disableProperty().bind(editableProperty.not());
    outputPropertySheet.setModeSwitcherVisible(false);
    outputPropertySheet.setSearchBoxVisible(false);
    outputPropertySheet.disableProperty().bind(editableProperty.not());

    txVbox.managedProperty().bind(txVbox.visibleProperty());
    txVbox.visibleProperty().bind(isSceenShowMode.not());
    rxVbox.managedProperty().bind(rxVbox.visibleProperty());
    rxVbox.visibleProperty().bind(isSceenShowMode);

    txPropertyBox.managedProperty().bind(txPropertyBox.visibleProperty());
    txPropertyBox.visibleProperty().bind(isSceenShowMode.not());
    rxPropertyBox.managedProperty().bind(rxPropertyBox.visibleProperty());
    rxPropertyBox.visibleProperty().bind(isSceenShowMode);

    ApplicationBase app = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    app.getMainWindow().getScene().addEventFilter(MouseEvent.MOUSE_CLICKED, evt -> {
      Node source = evt.getPickResult().getIntersectedNode();

      // move up through the node hierarchy until a TableRow or scene root is found
      while (source != null && !(source instanceof TableRow)) {
        source = source.getParent();
      }


      // clear selection on click anywhere but on a filled row
      if (source == null || ((TableRow<?>) source).isEmpty()) {
        tableView.getSelectionModel().clearSelection();
      }
    });
    tableView.setItems(vpConDataList);
    tableView.setOnDragDetected(
        (event) -> {
          VpConsoleData selected = tableView.getSelectionModel().getSelectedItem();
          if (selected != null) {

            Dragboard db = tableView.startDragAndDrop(TransferMode.ANY);
            ClipboardContent content = new ClipboardContent();
            content.putString(selected.getId() + "");
            db.setContent(content);
          }
          event.consume();
        });

    Bindings.bindContent(layoutPropertySheet.getItems(), videoWallView.getControllable().getLayoutPropperties());
    Bindings.bindContent(
        videoLayoutPropertySheet.getItems(),
        videoWallView.getControllable().getVideoLayoutPropperties());

    // 视频窗口列表
    if (func.getVideoWallObject() instanceof CaesarVideoWallData) {
      windowsTable.setItems(((CaesarVideoWallData) func.getVideoWallObject()).getVideos());

      windowsTable.setFixedCellSize(27);
      windowsTable
          .prefHeightProperty()
          .bind(
              tableView
                  .fixedCellSizeProperty()
                  .multiply(Bindings.size(windowsTable.getItems()).add(1.01)));
      windowsTable.minHeightProperty().bind(tableView.prefHeightProperty());
      windowsTable.maxHeightProperty().bind(tableView.prefHeightProperty());

      windowNameColumn.setCellValueFactory((feat) -> feat.getValue().getName());
      windowSourceColumn.setCellValueFactory((feat) -> feat.getValue().getSource());
      windowSourceColumn.setCellFactory((column) -> new VideoSourceCell());
    }
    // logicLayout
    logicLayoutPropertySheet.getItems().setAll(getLogicLayoutProperties());
    // output
    outputPropertySheet.getItems().setAll(getOutputProperties());
    // 音频组
    audioGroupPropertySheet.getItems().setAll(getAudioGroupProperties());
    // 测试画面
    testScreenPropertySheet.getItems().setAll(getTestScreenProperties());

    //
    configPropertySheet.getItems().setAll(getConfigProperties());
    updateVideoSourceList(func);
  }

  protected Collection<PropertySheet.Item> getLogicLayoutProperties() {
    List<PropertySheet.Item> logicLayoutProperties = new ArrayList<>();
    CaesarVideoWallData videoWallData = (CaesarVideoWallData) func.getVideoWallObject();
    BooleanProperty useLogicLayout = videoWallData.getUseLogicLayout();
    BooleanPropertyItem2 enable = new BooleanPropertyItem2(useLogicLayout);
    enable.setName(CaesarI18nCommonResource.getString("videowall.logiclayout.enable"));
    logicLayoutProperties.add(enable);
    LogicLayoutData logicLayoutData = videoWallData.getLogicLayoutData();
    NumberPropertyItem row =
        new NumberPropertyItem(logicLayoutData.getRowsProperty(), Integer.class);
    row.setName(CaesarI18nCommonResource.getString("videowall.logiclayout.row"));
    logicLayoutProperties.add(row);
    NumberPropertyItem column =
        new NumberPropertyItem(logicLayoutData.getColumnsProperty(), Integer.class);
    column.setName(CaesarI18nCommonResource.getString("videowall.logiclayout.column"));
    logicLayoutProperties.add(column);
    return logicLayoutProperties;
  }

  protected Collection<PropertySheet.Item> getOutputProperties() {
    List<PropertySheet.Item> outputProperties = new ArrayList<>();
    CaesarVideoWallData videoWallData = (CaesarVideoWallData) func.getVideoWallObject();
    CaesarOutputData outputData = videoWallData.getOutputData();
    BooleanPropertyItem2 enable = new BooleanPropertyItem2(outputData.enable);
    enable.setName(CaesarI18nCommonResource.getString("videowall.output.enable"));
    outputProperties.add(enable);

    NumberPropertyItem clock = new NumberPropertyItem(outputData.clock, Integer.class);
    clock.setName(CaesarI18nCommonResource.getString("videowall.output.clock"));
    outputProperties.add(clock);

    NumberPropertyItem vertSync = new NumberPropertyItem(outputData.vertSync, Integer.class);
    vertSync.setName(CaesarI18nCommonResource.getString("videowall.output.vert_sync"));
    outputProperties.add(vertSync);

    NumberPropertyItem vertBackPorch =
        new NumberPropertyItem(outputData.vertBackPorch, Integer.class);
    vertBackPorch.setName(CaesarI18nCommonResource.getString("videowall.output.vert_back_porch"));
    outputProperties.add(vertBackPorch);

    NumberPropertyItem vertFrontPorch =
        new NumberPropertyItem(outputData.vertFrontPorch, Integer.class);
    vertFrontPorch.setName(CaesarI18nCommonResource.getString("videowall.output.vert_front_porch"));
    outputProperties.add(vertFrontPorch);

    BooleanPropertyItem2 vertPolarity = new BooleanPropertyItem2(outputData.vertPolarity);
    vertPolarity.setName(CaesarI18nCommonResource.getString("videowall.output.vert_polarity"));
    outputProperties.add(vertPolarity);

    NumberPropertyItem horzSync = new NumberPropertyItem(outputData.horzSync, Integer.class);
    horzSync.setName(CaesarI18nCommonResource.getString("videowall.output.horz_sync"));
    outputProperties.add(horzSync);

    NumberPropertyItem horzBackPorch =
        new NumberPropertyItem(outputData.horzBackPorch, Integer.class);
    horzBackPorch.setName(CaesarI18nCommonResource.getString("videowall.output.horz_back_porch"));
    outputProperties.add(horzBackPorch);

    NumberPropertyItem horzFrontPorch =
        new NumberPropertyItem(outputData.horzFrontPorch, Integer.class);
    horzFrontPorch.setName(CaesarI18nCommonResource.getString("videowall.output.horz_front_porch"));
    outputProperties.add(horzFrontPorch);

    BooleanPropertyItem2 horzPolarity = new BooleanPropertyItem2(outputData.horzPolarity);
    horzPolarity.setName(CaesarI18nCommonResource.getString("videowall.output.horz_polarity"));
    outputProperties.add(horzPolarity);

    return outputProperties;
  }

  protected Collection<PropertySheet.Item> getAudioGroupProperties() {
    CaesarVideoWallData videoWallData = (CaesarVideoWallData) func.getVideoWallObject();
    ObjectProperty<String> property = new SimpleObjectProperty<>();
    for (TxRxGroupData activeTxRxGroup : deviceController.getDataModel().getConfigDataManager()
        .getTxRxGroups()) {
      if (activeTxRxGroup.getOid() + 1 == videoWallData.getAudioGroupIndex().get()) {
        property.set(activeTxRxGroup.getName());
      }
    }
    videoWallData.getAudioGroupIndex().addListener((obs, oldVal, newVal) -> {
      property.set("");
      for (TxRxGroupData activeTxRxGroup : deviceController.getDataModel().getConfigDataManager()
          .getTxRxGroups()) {
        if (activeTxRxGroup.getOid() + 1 == newVal) {
          property.set(activeTxRxGroup.getName());
        }
      }
    });
    TextBlockPropertyItem groupItem = new TextBlockPropertyItem(property, true);
    groupItem.setName(Bundle.NbBundle.getMessage("audio.output"));
    groupItem.setTextAction(event -> {
      List<Integer> audioGroups =
          deviceController.getDataModel().getConfigDataManager().getActiveTxRxGroups().stream()
              .filter(item -> TxRxGroupDataType.RX_AUDIO_GROUP.equals(item.getType())).map(
                  item -> item.getOid() + 1)
              .collect(Collectors.toList());
      ObjectProperty<Integer> audioGroupIndex = videoWallData.getAudioGroupIndex();
      List<Integer> allVideoWallAudioGroups =
          Arrays.stream(deviceController.getDataModel().getVpDataModel().getAllVideoWalls())
              .map(VideoWallGroupData.VideoWallData::getAudioGroupIndex)
              .collect(Collectors.toList());
      ObservableList<Integer> listChoices = FXCollections.observableArrayList();
      listChoices.add(0);
      for (Integer audioGroup : audioGroups) {
        if (allVideoWallAudioGroups.contains(audioGroup)
            && !audioGroup.equals(audioGroupIndex.get())) {
          continue;
        }
        listChoices.add(audioGroup);
      }

      ApplicationBase applicationBase =
          InjectorProvider.getInjector().getInstance(ApplicationBase.class);
      AudioGroupSelectDialog dialog =
          new AudioGroupSelectDialog(applicationBase.getMainWindow(), audioGroupIndex.get(),
              listChoices,
              deviceController);
      dialog.showAndWait().ifPresent(selectedGroup -> {
        if (selectedGroup.equals(audioGroupIndex.get())) {
          return;
        }
        // 断开所有音频rx连接
        final int audioGroup = audioGroupIndex.get();
        deviceController.execute(() -> {
          for (ConsoleData consoleData : deviceController.getDataModel().getConfigDataManager()
              .getActiveConsolesWithoutVpcon()) {
            if (consoleData.getGroupIndex() == audioGroup) {
              ExtendedSwitchUtility.disconnect(deviceController.getDataModel(), consoleData, true);
            }
          }
        });
        // 断开所有音频输入
        for (CaesarVideoData video : videoWallData.getVideos()) {
          video.getAudioRx().set(0);
        }
        videoWallData.getAudioGroupIndex().set(selectedGroup);
      });
    });

    List<PropertySheet.Item> result = new ArrayList<>();
    result.add(groupItem);

    if (videoWallData.isVp7()) {
      NumberPropertyItem analogVolume = new NumberPropertyItem(videoWallData.getOsdData().getAnalogAudioVolumeProperty(),
          Integer.class);
      analogVolume.setName(Bundle.NbBundle.getMessage("Video.analogAudioVolume"));
      analogVolume.setValueRange(0, 255);
      result.add(analogVolume);
    }
    return result;
  }

  protected Collection<PropertySheet.Item> getTestScreenProperties() {
    CaesarVideoWallData videoWallData = (CaesarVideoWallData) func.getVideoWallObject();
    CaesarTestFrameData testFrameData = videoWallData.getTestFrameData();
    // 测试画面开启状态
    BooleanProperty testFrameEnableProperty = new SimpleBooleanProperty(false);
    BooleanBinding testFrameEnable =
        Bindings.createBooleanBinding(() -> testFrameData.getMode().getValue() != 0,
            testFrameData.modeProperty());
    testFrameEnableProperty.bind(testFrameEnable);
    BooleanPropertyItem2 enable = new BooleanPropertyItem2(testFrameEnableProperty);
    enable.setName(CaesarI18nCommonResource.getString("enable"));
    enable.setEditable(false);
    List<PropertySheet.Item> result = new ArrayList<>();
    result.add(enable);

    // 测试画面模式
    EnumPropertyItem<CaesarTestFrameMode> item =
        new EnumPropertyItem<>(testFrameData.modeProperty(), CaesarTestFrameMode.class);
    item.setName(Bundle.NbBundle.getMessage("test_frame.mode"));
    item.setConverter(new TestFrameModeConverter());
    result.add(item);

    ObjectPropertyItem<Color> customColors =
        new ObjectPropertyItem<>(videoWallData.getTestFrameData().colorProperty(), Color.class);
    customColors.setName(Bundle.NbBundle.getMessage("test_frame.custom_color"));
    result.add(customColors);
    // 测试画面速度
    NumberPropertyItem speed =
        new NumberPropertyItem(videoWallData.getTestFrameData().speedProperty(), Integer.class);
    int maxSpeed = 255;
    speed.setValueRange(0, maxSpeed);
    speed.setName(Bundle.NbBundle.getMessage("test_frame.speed"));
    result.add(speed);
    // 测试画面透明度
    NumberPropertyItem alpha =
        new NumberPropertyItem(videoWallData.getTestFrameData().alphaProperty(), Double.class, new AlphaNumberConverter());
    alpha.setValueRange(0, 1.0);
    alpha.setName(Bundle.NbBundle.getMessage("test_frame.alpha"));
    result.add(alpha);

    return result;
  }

  protected Collection<PropertySheet.Item> getConfigProperties() {
    EnumPropertyItem<PublishMode> item =
        new EnumPropertyItem<>(func.getPublishModeProperty(), PublishMode.class);
    item.setName(CaesarI18nCommonResource.getString("videowall.mode"));
    item.setId("publish-mode");
    item.setConverter(new PublishModeConverter());
    List<PropertySheet.Item> result = new ArrayList<>();
    result.add(item);

    if (func.getCaesarVideoWall().isVp7()) {
      TextBlockPropertyItem resyncItem = new TextBlockPropertyItem(new SimpleObjectProperty<>(""));
      resyncItem.setName(CaesarI18nCommonResource.getString("videowall.resync"));
      resyncItem.setValue(CaesarI18nCommonResource.getString("videowall.resync.hint"));
      resyncItem.setTextAction(
          (event) -> {
            ApplicationBase applicationBase = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
            AlertEx confirm = new UndecoratedAlert(AlertExType.CONFIRMATION);
            confirm.initOwner(applicationBase.getMainWindow());
            confirm.setHeaderText(null);
            confirm.setContentText(CaesarI18nCommonResource.getString("videowall.resync.confirm"));
            confirm.showAndWait().ifPresent(response -> {
              if (response == ButtonType.OK) {
                deviceController.execute(() -> {
                  try {
                    deviceController.getDataModel().getController().setVideoWallResyncVideo(func.getVideoWallIndex());
                  } catch (Exception e) {
                    log.error("Failed to resync video wall", e);
                  }
                });
              }
            });
          });
      result.add(resyncItem);
    }
    return result;
  }

  @FXML
  protected void onPublish() {
    videoWallView.getControllable().publish();
  }

  @FXML
  protected void onSaveScenario() {
    videoWallView.getControllable().saveScenario();
  }

  @FXML
  protected void onSaveAsScenario() {
    videoWallView.getControllable().saveAsScenario();
  }

  @FXML
  protected void onSwitch() {
    if (switchFunctional != null) {
      switchFunctional.onSwitch();
    }
  }

  @FXML
  protected void onConfig() {
  }

  @FXML
  protected void onSceenMode() {
    isSceenShowMode.set(isSceenShowMode.not().get());
    VideoWallControllable videoWallControllable = videoWallView.getControllable();
    if (isSceenShowMode.get()) {
      videoWallControllable.switchScreenMode();
    } else {
      videoWallControllable.switchVideoMode();
    }
  }

  @FXML
  protected void autoArrange() {
    VideoWallControllable videoWallControllable = videoWallView.getControllable();
    RearrangeDialog.show(
        this.getScene().getWindow(),
        func,
        videoWallControllable,
        vpConDataList,
        findCaesarMatrix());
  }

  private CaesarMatrix findCaesarMatrix() {
    VisualEditNode node = this.func;
    while (node != null && !(node instanceof CaesarMatrix)) {
      node = node.getParent();
    }
    return (CaesarMatrix) node;
  }

  @Override
  public ViewControllable getViewControllable() {
    return this;
  }

  @Override
  public Pane getPreviewContainer() {
    return videoPreviewContainer;
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
      if (videoWallView != null) {
        videoWallView.getControllable().setDeviceController(deviceController);
      }
    }
  }

  @Override
  public DeviceControllable getDeviceController() {
    return deviceController;
  }

  private static class IndexCell extends TableCell<VpConsoleData, String> {
    @Override
    public void updateItem(String item, boolean empty) {
      super.updateItem(item, empty);
      this.setText(null);
      this.setGraphic(null);

      if (!empty) {
        int rowIndex = this.getIndex() + 1;
        this.setText(String.valueOf(rowIndex));
      }
    }
  }

  private static class VideoSourceCell extends TableCell<CaesarVideoData, VisualEditTerminal> {
    @Override
    protected void updateItem(VisualEditTerminal item, boolean empty) {
      super.updateItem(item, empty);
      if (!empty && item != null && getTableRow().getItem() != null) {
        CaesarVideoData caesarVideoData = (CaesarVideoData) getTableRow().getItem();
        textProperty().bind(Bindings.createStringBinding(() -> String.format("%s%s%s", item.getName(),
                item.canSeperate() ? "(" + (caesarVideoData.getSourceIndex().get() + 1) + ")" : "",
                caesarVideoData.getCropIndex().get() > 0
                    ? "@" + (Strings.isNullOrEmpty(caesarVideoData.getCropName().get()) ? "(empty)" :
                    caesarVideoData.getCropName().get()) : ""),
            item.nameProperty(), caesarVideoData.getSourceIndex(), caesarVideoData.getCropIndex(),
            caesarVideoData.getCropName()));
      } else {
        textProperty().unbind();
        textProperty().set("");
      }
    }
  }

  private static class PublishModeConverter extends StringConverter<PublishMode> {

    @Override
    public String toString(PublishMode object) {
      if (object == PublishMode.AUTO) {
        return CaesarI18nCommonResource.getString("videowall.mode.auto");
      } else if (object == PublishMode.MANUAL) {
        return CaesarI18nCommonResource.getString("videowall.mode.manual");
      } else {
        return "UNKNOWN";
      }
    }

    @Override
    public PublishMode fromString(String string) {
      return null;
    }
  }

  private static class TestFrameModeConverter extends StringConverter<CaesarTestFrameMode> {

    @Override
    public String toString(CaesarTestFrameMode object) {
      return object.getName();
    }

    @Override
    public CaesarTestFrameMode fromString(String string) {
      return null;
    }
  }

  @Override
  public void close() {
    super.close();
    sourceTree.destroy();
    videoWallView.getOperationControllable().close();
  }

  @Override
  public void refreshOnShow() {
    super.refreshOnShow();
    PlatformUtility.runInFxThread(() -> videoWallView.getOperationControllable().refreshOnShow());
  }
}
