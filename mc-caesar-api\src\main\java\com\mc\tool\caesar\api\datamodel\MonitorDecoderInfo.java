package com.mc.tool.caesar.api.datamodel;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 监控解码器信息数据模型.
 */
@Setter
@Getter
@NoArgsConstructor
@ToString
public class MonitorDecoderInfo {
  /**
   * 网格类型枚举.
   */
  public enum GridType {
    NONE(0), // 无
    GRID_4(1), // 4分屏
    GRID_9(2), // 9分屏
    GRID_16(3), // 16分屏
    GRID_25(4); // 25分屏

    private final int value;
    GridType(int value) {
      this.value = value;
    }

    public int getValue() {
      return value;
    }

    /**
     * 根据value获取GridType.
     *
     * @param value 整数值
     * @return GridType
     */
    public static GridType valueOf(int value) {
      for (GridType type : GridType.values()) {
        if (type.getValue() == value) {
          return type;
        }
      }
      return NONE; // 默认返回NONE
    }
  }

  /**
   * 设置IP地址.
   *
   * @param ip IP地址的字节数组，长度必须为4
   */
  public void setIp(byte[] ip) {
    if (ip != null && ip.length == 4) {
      System.arraycopy(ip, 0, this.ip, 0, 4);
    } else {
      throw new IllegalArgumentException("IP address must be a byte array of length 4.");
    }
  }

  public byte[] getIp() {
    return ip.clone(); // 返回IP地址的副本，避免外部修改
  }

  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  private byte []ip = new byte[4]; // 监控解码器IP地址
  private int port = 0; // 端口号
  private GridType gridType = GridType.NONE; // 网格类型
}
