package com.mc.tool.caesar.vpm.pages.operation.videowall.vp;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.MultiviewData;
import com.mc.tool.caesar.api.datamodel.ResolutionData;
import com.mc.tool.caesar.api.datamodel.SourceCropData;
import com.mc.tool.caesar.api.datamodel.vp.Vp6ConfigData;
import com.mc.tool.caesar.api.datamodel.vp.Vp6OutputData;
import com.mc.tool.caesar.api.datamodel.vp.Vp7ConfigData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.util.vp.Vp7InfType;
import com.mc.tool.caesar.vpm.util.vp.VpMatrix;
import com.mc.tool.caesar.vpm.util.vp.VpResolution;
import com.mc.tool.caesar.vpm.util.vp.VpTxCropData;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarVpMatrix implements VpMatrix {
  private CaesarDeviceController controller;

  public CaesarVpMatrix(CaesarDeviceController controller) {
    this.controller = controller;
  }

  @Override
  public VpResolution getTxCurrentResolution(int txid, int sourceIndex) {
    ExtenderData extenderData = findExt(txid);
    if (extenderData != null && extenderData.getCpuData() != null) {
      VpResolution.Type type = VpResolution.Type.VIDEO_2K;
      switch (extenderData.getExtenderStatusInfo().getVideoResolutionType()) {
        case VIDEO_2K:
          type = VpResolution.Type.VIDEO_2K;
          break;
        case VIDEO_4K_30HZ:
          type = VpResolution.Type.VIDEO_4K30;
          break;
        case VIDEO_4K_60HZ:
          type = VpResolution.Type.VIDEO_4K60;
          break;
        default:
          log.warn("Unknown resolution type: {}!", extenderData.getExtenderStatusInfo().getVideoResolutionType());
      }
      ResolutionData item = extenderData.getResolution(sourceIndex);
      return new VpResolution((int) item.getWidth(), (int) item.getHeight(), type);
    } else {
      log.warn("Fail to find tx: {}!", txid);
      return new VpResolution();
    }
  }

  @Override
  public VpResolution getTxFinalResolution(int txid, int sourceIndex) {
    ExtenderData extenderData = findExt(txid);
    if (extenderData != null
        && extenderData.getExtenderStatusInfo().getSpecialExtType() == CaesarConstants.Extender.SpecialExtenderType.HW_FUSION) {
      return new VpResolution(3840, 2160, VpResolution.Type.VIDEO_4K30);
    } else {
      return getTxCurrentResolution(txid, sourceIndex);
    }
  }

  @Override
  public VpTxCropData getTxCropData(int txid, int sourceIndex, int sourceCropIndex) {
    ExtenderData extenderData = findExt(txid);
    VpTxCropData result = new VpTxCropData(0, 0, 0, 0);
    if (extenderData != null && extenderData.getCpuData() != null && sourceCropIndex > 0) {
      SourceCropData cropData = controller.getDataModel().getConfigData().getSourceCropData(sourceCropIndex - 1);
      if (cropData != null && cropData.getCpuIndex() == extenderData.getCpuData().getOid() + 1 && sourceIndex == cropData.getSourceIndex()) {
        result = new VpTxCropData(cropData.getLeft(), cropData.getTop(), cropData.getRight(), cropData.getBottom());
      } else {
        log.warn("Fail to find crop data for tx: {} source: {} crop: {}!", txid, sourceIndex, sourceCropIndex - 1);
      }
    }
    return result;
  }

  @Override
  public boolean isRxOnline(int rxid) {
    for (ConsoleData data : controller.getDataModel().getConfigDataManager().getActiveConsoles()) {
      if (data.getId() == rxid) {
        return data.isOnline();
      }
    }
    return false;
  }

  @Override
  public boolean isExtOnline(int extid) {
    ExtenderData extenderData = findExt(extid);
    return extenderData != null && (extenderData.getPort() > 0 || extenderData.getRdPort() > 0);
  }

  @Override
  public int getTxIdByExtId(int extid) {
    ExtenderData extenderData = findExt(extid);
    if (extenderData != null && extenderData.getCpuData() != null) {
      return extenderData.getCpuData().getId();
    } else {
      return 0;
    }
  }

  @Override
  public int getRxIdByExtId(int extid) {
    ExtenderData extenderData = findExt(extid);
    if (extenderData != null && extenderData.getConsoleData() != null) {
      return extenderData.getConsoleData().getId();
    } else {
      return 0;
    }
  }

  @Override
  public boolean hasDualInput(int txid) {
    ExtenderData extenderData = findExt(txid);
    return extenderData != null
        && extenderData.getExtenderStatusInfo() != null
        && extenderData.getExtenderStatusInfo().getInterfaceCount() > 1;
  }

  @Override
  public Vp7InfType getVp7InfType(int vp7id) {
    for (ConsoleData consoleData : controller.getDataModel().getConfigDataManager().getActiveConsoles()) {
      MultiviewData multiviewData;
      if (consoleData.getId() == vp7id && (multiviewData = consoleData.getMultiviewData()) != null) {
        switch (multiviewData.getOutputMode()) {
          case FOUR_INTERFACE:
            return Vp7InfType.TYPE_QUADRUPLE;
          case TWO_INTERFACE:
            return Vp7InfType.TYPE_DOUBLE;
          case ONE_INTERFACE:
            return Vp7InfType.TYPE_SINGLE;
          default:
            log.warn("Unknown output mode: {}!", multiviewData.getOutputMode());
            return Vp7InfType.TYPE_QUADRUPLE;
        }
      }
    }
    log.warn("Fail to find vp7: {}!", vp7id);
    return Vp7InfType.TYPE_QUADRUPLE;
  }

  @Override
  public void sendVp6ConfigData(VpConsoleData vpConsoleData, Vp6ConfigData vp6ConfigData, Vp6OutputData outputData) {

  }

  @Override
  public void connectVideo(int rxid, int txid, int sourceIndex) {

  }

  @Override
  public void connectMultiviewVideo(int rxid, int txCnt, int[] txes) {

  }

  @Override
  public void sendVp7ConfigData(int vp7id, Vp7ConfigData data) {

  }

  @Override
  public ExtenderData findExt(int txId) {
    if (txId == 0) {
      return null;
    }

    for (ExtenderData extenderData :
        controller.getDataModel().getConfigDataManager().getActiveExtenders()) {
      if (extenderData.getId() == txId) {
        return extenderData;
      }
    }
    return null;
  }

  @Override
  public VpConsoleData findVpCon(int vpConId) {
    for (VpConsoleData vpConsoleData :
        controller.getDataModel().getConfigDataManager().getActiveVpconsolses()) {
      if (vpConsoleData.getId() == vpConId) {
        return vpConsoleData;
      }
    }
    return null;
  }
}
