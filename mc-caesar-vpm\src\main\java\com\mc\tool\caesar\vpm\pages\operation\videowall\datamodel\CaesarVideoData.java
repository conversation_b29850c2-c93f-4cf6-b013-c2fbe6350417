package com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel;

import com.google.common.base.Strings;
import com.mc.tool.framework.operation.videowall.datamodel.VideoData;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ReadOnlyStringProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.beans.value.ChangeListener;
import lombok.Getter;

/**
 * .
 */
public class CaesarVideoData extends VideoData {

  @Getter
  private IntegerProperty sourceIndex = new SimpleIntegerProperty(0);
  @Getter
  private IntegerProperty cropIndex = new SimpleIntegerProperty(0);
  @Getter
  private StringProperty cropName = new SimpleStringProperty("");
  // 用于输出音频的RX ID,0表示无输出
  @Getter
  private IntegerProperty audioRx = new SimpleIntegerProperty(0);
  @Getter
  private IntegerProperty audioSeq = new SimpleIntegerProperty(0);
  private ChangeListener<?> sourceChangeListener;

  public CaesarVideoData() {
  }

  @Override
  public void copyTo(VideoObject videoData) {
    super.copyTo(videoData);
    if (videoData instanceof CaesarVideoData) {
      ((CaesarVideoData) videoData).getSourceIndex().set(sourceIndex.get());
      ((CaesarVideoData) videoData).getCropIndex().set(cropIndex.get());
      ((CaesarVideoData) videoData).getCropName().set(cropName.get());
      ((CaesarVideoData) videoData).getAudioRx().set(audioRx.get());
      ((CaesarVideoData) videoData).getAudioSeq().set(audioSeq.get());
    }
  }

  @Override
  public ReadOnlyStringProperty getNameWithSource() {
    if (sourceChangeListener == null) {
      sourceChangeListener =
          weakAdapter.wrap(
              (obs, oldVal, newVal) -> {
                if (nameWithSource != null) {
                  nameWithSource.set(createNameWithSource());
                }
              });
      sourceIndex.addListener((ChangeListener<Number>) sourceChangeListener);
      cropIndex.addListener((ChangeListener<Number>) sourceChangeListener);
      cropName.addListener((ChangeListener<String>) sourceChangeListener);
      audioRx.addListener((ChangeListener<Number>) sourceChangeListener);
    }
    return super.getNameWithSource();
  }

  @Override
  protected String createNameWithSource() {
    String sourceFullName = getSourceFullName();
    // 是否有音频输出
    if (audioRx.get() > 0 && !Strings.isNullOrEmpty(sourceFullName)) {
      return String.format("%s[%s]<audio_%d>", name.get(),  sourceFullName, audioRx.get());
    }
    return String.format("%s%s", name.get(), Strings.isNullOrEmpty(sourceFullName) ? "" : "[" + sourceFullName + "]");
  }

  protected String getSourceFullName() {
    if (source.get() == null) {
      return "";
    } else {
      return String.format("%s%s%s", source.get().getName(),
          source.get().canSeperate() ? "(" + (getSourceIndex().get() + 1) + ")" : "",
          cropIndex.get() > 0 ? "@" + (Strings.isNullOrEmpty(cropName.get()) ? "(empty)" : cropName.get()) : "");
    }
  }
}
